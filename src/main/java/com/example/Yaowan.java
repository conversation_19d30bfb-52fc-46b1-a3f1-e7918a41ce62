package com.example;

import com.microsoft.playwright.*;
import com.microsoft.playwright.options.Cookie;

public class Yaowan {
    public static void main(String[] args) {
        // 设置浏览器下载路径到E盘
        System.setProperty("PLAYWRIGHT_BROWSERS_PATH", "E:\\playwright-browsers");

        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(new BrowserType.LaunchOptions()
                    .setHeadless(false));

            BrowserContext context = browser.newContext();

            // 添加 cookies
            context.addCookies(java.util.Arrays.asList(
                    new <PERSON><PERSON>("flarum_remember", "DJmomm4sayDo4L7FS7ijSfT6jwdtNFPDxX2t0L1x")
                            .setDomain("invites.fun")
                            .setPath("/"),
                    new <PERSON><PERSON>("flarum_session", "JNaO3QpzcraHScyE5F696SjhDicbtLG1CxBfNikh")
                            .setDomain("invites.fun")
                            .setPath("/")
            ));

            Page page = context.newPage();
            page.navigate("https://invites.fun/");

            page.waitForLoadState();

            String title = page.title();
            System.out.println("页面标题: " + title);

            // 循环查找 img.Header-logo，20次，每秒一次
            boolean logoFound = false;
            for (int i = 0; i < 20; i++) {
                try {
                    Locator logo = page.locator("img.Header-logo");
                    if (logo.count() > 0) {
                        System.out.println("找到 Header-logo");
                        logoFound = true;
                        break;
                    }
                } catch (Exception e) {
                    // 忽略异常，继续循环
                }
                page.waitForTimeout(1000); // 等待1秒
            }

            if (!logoFound) {
                System.out.println("未找到 Header-logo，退出");
                browser.close();
                return;
            }

            // 查找 button.CheckInButton--green
            try {
                Locator greenButton = page.locator("button.CheckInButton--green");
                if (greenButton.count() > 0) {
                    System.out.println("找到 CheckInButton--green，退出");
                    browser.close();
                    return;
                }
            } catch (Exception e) {
                // 忽略异常
            }

            // 点击 Button CheckInButton
            try {
                Locator checkInButton = page.locator("Button.CheckInButton");
                if (checkInButton.count() > 0) {
                    checkInButton.click();
                    System.out.println("点击了 CheckInButton");
                } else {
                    System.out.println("未找到 CheckInButton");
                }
            } catch (Exception e) {
                System.out.println("点击 CheckInButton 失败: " + e.getMessage());
            }

            page.waitForTimeout(3000);
            browser.close();
        }
    }
}